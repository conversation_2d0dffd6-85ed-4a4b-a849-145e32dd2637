<?php

namespace App\Services;

use Throwable;
use App\Models\Teacher;
use App\Enums\UserStatus;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Eloquent\Collection;

use App\Exceptions\BusinessLogicException;
use App\Exceptions\TeacherException;
use Illuminate\Database\Eloquent\ModelNotFoundException; // Import ModelNotFoundException
use App\Repositories\Contracts\TeacherRepositoryInterface;

class TeacherService
{
    protected TeacherRepositoryInterface $teacherRepository;
    protected UserService $userService;

    public function __construct(
        TeacherRepositoryInterface $teacherRepository,
        UserService $userService
    ) {
        $this->teacherRepository = $teacherRepository;
        $this->userService = $userService;
    }

    /**
     * Get all teachers with optional filtering.
     */
    public function getAll(array $filters = []): Collection
    {
        return $this->teacherRepository->getAll($filters);
    }

    /**
     * Get all active teachers.
     */
    public function getAllActiveTeachers(): Collection
    {
        return $this->teacherRepository->getAllActive();
    }

    /**
     * Find a teacher by ID.
     * Converts ModelNotFoundException to BusinessLogicException for consistency.
     */
    public function findById(int $id): Teacher
    {
        try {
            return $this->teacherRepository->findById($id);
        } catch (ModelNotFoundException $e) {
            throw new BusinessLogicException("Guru tidak ditemukan.");
        }
    }

    /**
     * Create a new teacher with user account.
     * Logika penetapan peran dan pembuatan user diatur di sini.
     * Validasi unik email/username diasumsikan di FormRequest.
     */
    public function create(array $data): Teacher
    {
        return DB::transaction(function () use ($data) {
            try {
                // Buat user melalui UserService
                // UserService sudah menangani hashing password dan penugasan peran
                $user = $this->userService->create([
                    'name' => $data['name'],
                    'username' => $data['username'],
                    'email' => $data['email'],
                    'password' => $data['password'],
                    'phone_number' => $data['phone_number'] ?? null,
                    'role' => $data['role'], // Role akan diassign di UserService
                    'status' => $data['status'] ?? UserStatus::Active->value,
                ]);

                $teacherData = [
                    'birth_place' => $data['birth_place'],
                    'birth_date' => $data['birth_date'],
                    'gender' => $data['gender'],
                    'user_id' => $user->id,
                ];

                // Buat guru melalui TeacherRepository
                return $this->teacherRepository->create($teacherData)->load('user');
            } catch (BusinessLogicException | TeacherException $e) {
                // Re-throw pengecualian bisnis dari UserService atau TeacherService
                throw $e;
            } catch (Throwable $e) {
                // Tangkap pengecualian generik dan ubah menjadi BusinessLogicException
                throw new BusinessLogicException('Gagal membuat data guru: ' . $e->getMessage());
            }
        });
    }

    /**
     * Update teacher data.
     * Logika pembaruan user terkait dan validasi diatur di sini.
     * Validasi unik email/username diasumsikan di FormRequest.
     */
    public function update(int $id, array $data): bool
    {
        return DB::transaction(function () use ($id, $data) {
            try {
                $teacher = $this->findById($id); // Akan melempar BusinessLogicException jika tidak ditemukan

                // Update data user terkait melalui UserService
                $userData = [
                    'name' => $data['name'] ?? null,
                    'email' => $data['email'] ?? null,
                    'username' => $data['username'] ?? null,
                    'password' => $data['password'] ?? null, // Biarkan UserService handle hashing & empty check
                    'phone_number' => $data['phone_number'] ?? null,
                    'role' => $data['role'] ?? null, // Role akan di-sync di UserService
                    'status' => $data['status'] ?? null, // Status akan di-update di UserService
                ];
                $this->userService->update($teacher->user_id, array_filter($userData)); // Gunakan array_filter untuk hapus null

                // Update data guru melalui TeacherRepository
                return $this->teacherRepository->update($id, $data);
            } catch (BusinessLogicException | TeacherException $e) {
                throw $e;
            } catch (Throwable $e) {
                throw new BusinessLogicException('Gagal memperbarui data guru: ' . $e->getMessage());
            }
        });
    }

    /**
     * Delete a teacher.
     */
    public function deleteTeacher(int $id): bool
    {
        return DB::transaction(function () use ($id) {
            try {
                $teacher = $this->findById($id);

                // Business validation: Cek jika guru memiliki penugasan aktif
                $this->validateTeacherDeletion($teacher);

                // Hapus user terkai
                $this->userService->delete($teacher->user_id);

                return $this->teacherRepository->delete($id);
            } catch (BusinessLogicException | TeacherException $e) {
                throw $e;
            } catch (Throwable $e) {
                throw new BusinessLogicException('Gagal menghapus data guru: ' . $e->getMessage());
            }
        });
    }

    /**
     * Validate if a teacher can be deleted.
     *
     * @param Teacher $teacher The teacher object to validate.
     * @throws \App\Exceptions\TeacherException
     */
    private function validateTeacherDeletion(Teacher $teacher): void
    {
        // Cek jika guru memiliki penugasan aktif
        if ($this->teacherRepository->hasActiveAssignments($teacher->id)) { // Panggil method baru di repo
            throw TeacherException::cannotDeleteWithAssignments();
        }
        // Tambahkan validasi bisnis lain jika diperlukan
    }
}
