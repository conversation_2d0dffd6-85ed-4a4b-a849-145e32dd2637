<?php

use App\Http\Controllers\Admin\AcademicYearController;
use App\Http\Controllers\Admin\AdminController;
use App\Http\Controllers\Admin\AttendanceController;
use App\Http\Controllers\Admin\ClassroomController;
use App\Http\Controllers\Admin\ClassScheduleController;
use App\Http\Controllers\Admin\LeaveRequestController;
use App\Http\Controllers\Admin\LessonHourController;
use App\Http\Controllers\Admin\ProgramController;
use App\Http\Controllers\Admin\ShiftController;
use App\Http\Controllers\Admin\StaffController;
use App\Http\Controllers\Admin\StudentController;
use App\Http\Controllers\Admin\SubjectController;
use App\Http\Controllers\Admin\TeacherAssignmentController;
use App\Http\Controllers\Admin\TeacherController;
use App\Http\Controllers\DisplayController;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Authentication Routes
|--------------------------------------------------------------------------
*/
Auth::routes();

/*
|--------------------------------------------------------------------------
| Public Routes
|--------------------------------------------------------------------------
*/
Route::get('/display/tv', [DisplayController::class, 'tvDisplay'])->name('display.tv');

/*
|--------------------------------------------------------------------------
| Admin Routes
|--------------------------------------------------------------------------
| All routes under this group require authentication and are prefixed with 'admin'
*/
Route::middleware('auth')->prefix('admin')->name('admin.')->group(function () {
    // Dashboard
    Route::get('/dashboard', [AdminController::class, 'index'])->name('dashboard');

    /*
    |------------------------------------------------------------------------
    | Staff Management Routes
    |------------------------------------------------------------------------
    */
    Route::resource('staff', StaffController::class)->except('show');
    /*
    |------------------------------------------------------------------------
    | Teacher Management Routes
    |------------------------------------------------------------------------
    */
    Route::resource('teachers', TeacherController::class)->except('show');
    /*
    |------------------------------------------------------------------------
    | Student Management Routes
    |------------------------------------------------------------------------
    */
    Route::resource('students', StudentController::class);
    Route::post('students/{student}/change-status', [StudentController::class, 'changeStatus'])->name('students.change-status');
    Route::post('students/{student}/update-account', [StudentController::class, 'updateAccount'])->name('students.update-account');

    // Student Enrollment Routes
    Route::prefix('students')->name('students.')->group(function () {
        Route::post('{student}/enroll', [StudentController::class, 'enroll'])->name('enroll');
        Route::post('enroll-multiple', [StudentController::class, 'enrollMultiple'])->name('enroll.multiple');
        Route::delete('{student}/unenroll/{classroom}/{academicYear}', [StudentController::class, 'unenroll'])->name('unenroll');
        Route::post('remove-from-classroom', [StudentController::class, 'removeFromClassroom'])->name('remove-from-classroom');
        Route::post('import', [StudentController::class, 'import'])->name('import');
        Route::get('export', [StudentController::class, 'export'])->name('export');
        Route::get('template', [StudentController::class, 'template'])->name('template');
    });

    /*
    |------------------------------------------------------------------------
    | Program Management Routes
    |------------------------------------------------------------------------
    */
    Route::resource('programs', ProgramController::class);

    /*
    |------------------------------------------------------------------------
    | Subject Management Routes
    |------------------------------------------------------------------------
    */
    Route::resource('subjects', SubjectController::class);
    Route::get('subjects/data', [SubjectController::class, 'getData'])->name('subjects.data');

    /*
    |------------------------------------------------------------------------
    | Lesson Hour Management Routes
    |------------------------------------------------------------------------
    */
    Route::resource('lesson-hours', LessonHourController::class);

    /*
    |------------------------------------------------------------------------
    | Teacher Assignment Management Routes
    |------------------------------------------------------------------------
    */
    Route::resource('teacher-assignments', TeacherAssignmentController::class);

    /*
    |------------------------------------------------------------------------
    | Class Schedule Management Routes
    |------------------------------------------------------------------------
    */
    Route::resource('class-schedules', ClassScheduleController::class);
    Route::get('class-schedules/data', [ClassScheduleController::class, 'getData'])->name('class-schedules.data');
    Route::get('class-schedules/{id}/edit-data', [ClassScheduleController::class, 'getEditData'])->name('class-schedules.edit-data');

    /*
    |------------------------------------------------------------------------
    | Academic Year Management Routes
    |------------------------------------------------------------------------
    */
    Route::resource('academic-years', AcademicYearController::class);
    Route::post('academic-years/{academicYear}/change-status', [AcademicYearController::class, 'changeStatus'])->name('academic-years.change-status');

    /*
    |------------------------------------------------------------------------
    | Classroom Management Routes
    |------------------------------------------------------------------------
    */
    Route::resource('classrooms', ClassroomController::class);
    Route::get('classrooms/{classroom}/academic-details', [ClassroomController::class, 'showAcademicDetails'])->name('classrooms.academic-details');
    Route::prefix('classrooms')->name('classrooms.')->group(function () {
        Route::post('{classroom}/add-students', [ClassroomController::class, 'addStudents'])->name('add-students');
        Route::delete('{classroom}/remove-student/{student}', [ClassroomController::class, 'removeStudent'])->name('remove-student');
        Route::post('{classroom}/change-status', [ClassroomController::class, 'changeStatus'])->name('change-status');
    });

    /*
    |------------------------------------------------------------------------
    | Shift Management Routes
    |------------------------------------------------------------------------
    */
    Route::resource('shifts', ShiftController::class);

    /*
    |------------------------------------------------------------------------
    | Attendance Management Routes
    |------------------------------------------------------------------------
    */
    Route::resource('attendances', AttendanceController::class);
    Route::get('attendances-summary', [AttendanceController::class, 'getAttendanceSummary'])->name('attendances.summary');
    Route::get('attendances-export', [AttendanceController::class, 'exportAttendance'])->name('attendances.export');
    Route::get('teacher-attendance-summary', [AttendanceController::class, 'getTeacherAttendanceSummary'])->name('attendances.teacher.summary');
    Route::get('teacher-attendance-detailed', [AttendanceController::class, 'getTeacherDetailedAttendanceSummary'])->name('attendances.teacher.detailed');
    Route::get('global-teacher-attendance', [AttendanceController::class, 'getGlobalTeacherAttendanceReport'])->name('attendances.teacher.global');

    /*
    |------------------------------------------------------------------------
    | Leave Request Management Routes
    |------------------------------------------------------------------------
    */
    Route::resource('leave-requests', LeaveRequestController::class);

    /*
    |------------------------------------------------------------------------
    | AJAX and API Routes
    |------------------------------------------------------------------------
    */
    Route::prefix('ajax')->name('ajax.')->group(function () {
        Route::get('classrooms/by-academic-year', [ClassroomController::class, 'getClassroomsByAcademicYear'])->name('classrooms.by.academic.year');
        Route::get('programs/list', [ProgramController::class, 'getProgramsList'])->name('programs.list');
        Route::get('academic-years/list', [AcademicYearController::class, 'getAcademicYearsList'])->name('academic-years.list');
        Route::get('students/by-classroom', [AttendanceController::class, 'getStudentsByClassroom'])->name('students.by.classroom');
        Route::get('class-schedules/by-classroom', [AttendanceController::class, 'getClassSchedulesByClassroom'])->name('class-schedules.by.classroom');
        Route::get('class-attendance', [AttendanceController::class, 'getClassAttendance'])->name('class.attendance');
    });

    Route::prefix('api')->name('api.')->group(function () {
        Route::get('students/available/{classroom}', [StudentController::class, 'getAvailableStudents'])->name('students.available');
    });
});
