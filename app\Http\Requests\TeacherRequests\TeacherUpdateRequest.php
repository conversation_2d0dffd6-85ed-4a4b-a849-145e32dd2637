<?php

namespace App\Http\Requests\TeacherRequests;

use App\Enums\GenderEnum;
use App\Enums\RoleEnum;
use App\Enums\UserStatus;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\Password;

class TeacherUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        $teacherId = $this->route('teacher');

        return [
            // User data
            'name' => ['sometimes', 'string', 'max:255'],
            'username' => ['sometimes', 'nullable', 'string', 'max:100', 'alpha_dash', Rule::unique('users', 'username')->ignore($this->getUserIdForTeacher($teacherId))],
            'email' => ['sometimes', 'string', 'email', 'max:255', Rule::unique('users', 'email')->ignore($this->getUserIdForTeacher($teacherId))],
            'password' => ['sometimes', 'nullable', 'string', Password::defaults()],
            'phone_number' => ['sometimes', 'nullable', 'string', 'max:20', 'regex:/^[0-9+\-\s()]+$/'],
            'role' => ['sometimes', 'string', Rule::in(RoleEnum::teacher())],
            'status' => ['sometimes', Rule::enum(UserStatus::class)],

            // Teacher profile data
            'birth_place' => ['sometimes', 'string', 'max:100'],
            'birth_date' => ['sometimes', 'date', 'before:today'],
            'gender' => ['sometimes', Rule::enum(GenderEnum::class)],
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'name' => 'Nama',
            'username' => 'Username',
            'email' => 'Email',
            'password' => 'Password',
            'phone_number' => 'Nomor telepon',
            'role' => 'Role',
            'status' => 'Status',
            'birth_place' => 'Tempat lahir',
            'birth_date' => 'Tanggal lahir',
            'gender' => 'Jenis kelamin',
        ];
    }

    /**
     * Get custom error messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'name.string' => 'Nama harus berupa teks.',
            'name.max' => 'Nama tidak boleh lebih dari 255 karakter.',
            'username.string' => 'Username harus berupa teks.',
            'username.max' => 'Username tidak boleh lebih dari 100 karakter.',
            'username.unique' => 'Username sudah digunakan.',
            'username.alpha_dash' => 'Username hanya boleh berisi huruf, angka, tanda hubung, dan garis bawah.',
            'email.string' => 'Email harus berupa teks.',
            'email.email' => 'Email harus berupa alamat email yang valid.',
            'email.max' => 'Email tidak boleh lebih dari 255 karakter.',
            'email.unique' => 'Email sudah digunakan.',
            'password.string' => 'Password harus berupa teks.',
            'password.min' => 'Password harus memiliki panjang minimal :min karakter.',
            'phone_number.string' => 'Nomor telepon harus berupa teks.',
            'phone_number.max' => 'Nomor telepon tidak boleh lebih dari 20 karakter.',
            'phone_number.regex' => 'Nomor telepon hanya boleh berisi angka, tanda plus, tanda hubung, spasi, atau tanda kurung.',
            'role.string' => 'Role harus berupa teks.',
            'role.in' => 'Role yang dipilih tidak valid untuk guru.',
            'status.enum' => 'Status yang dipilih tidak valid.',
            'birth_place.string' => 'Tempat lahir harus berupa teks.',
            'birth_place.max' => 'Tempat lahir tidak boleh lebih dari 100 karakter.',
            'birth_date.date' => 'Tanggal lahir harus berupa tanggal yang valid.',
            'birth_date.before' => 'Tanggal lahir harus sebelum hari ini.',
            'gender.enum' => 'Jenis kelamin yang dipilih tidak valid.',
        ];
    }

    /**
     * Get user ID for the teacher through service layer
     */
    private function getUserIdForTeacher(?int $teacherId): ?int
    {
        if ($teacherId === null) {
            return null;
        }

        try {
            $teacherService = app(\App\Services\TeacherService::class);
            $teacher = $teacherService->findById($teacherId);
            return $teacher->user_id;
        } catch (\Exception $e) {
            return null;
        }
    }
}
