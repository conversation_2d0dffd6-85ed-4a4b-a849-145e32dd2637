<?php

namespace App\Repositories\Contracts;

use App\Models\Teacher;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Database\Eloquent\Collection;
use Throwable;

interface TeacherRepositoryInterface
{
    /**
     * Get all teachers with optional filters.
     */
    public function getAll(array $filters = []): Collection;

    /**
     * Get all active teachers.
     */
    public function getAllActive(): Collection;

    /**
     * Find a teacher by ID.
     *
     * @throws ModelNotFoundException
     */
    public function findById(int $id): Teacher;

    /**
     * Create a new teacher record.
     */
    public function create(array $data): Teacher;

    /**
     * Update an existing teacher record.
     */
    public function update(int $id, array $data);

    /**
     * Delete a teacher record.
     */
    public function delete(int $id);

    /**
     * Check if teacher has active assignments.
     */
    public function hasActiveAssignments(int $teacherId): bool;
}
