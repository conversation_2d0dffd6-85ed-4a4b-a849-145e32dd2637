<?php $__env->startSection('title', 'Siswa'); ?>

<?php $__env->startSection('content'); ?>
    <?php echo $__env->make('admin.components.page-title', [
        'title' => 'Siswa',
        'breadcrumb' => 'Manajemen Akun',
    ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <!-- Card Header -->
                <div class="card-header border-bottom-dashed">
                    <div class="d-flex flex-column flex-sm-row justify-content-between align-items-center gap-3">
                        <!-- Title with Counter -->
                        <div>
                            <h5 class="card-title mb-0 d-flex align-items-center">
                                Daftar <?php echo $__env->yieldContent('title'); ?>
                                <span class="badge bg-primary-subtle text-primary ms-2" id="total-students">0</span>
                            </h5>
                        </div>

                        <!-- Action Buttons -->
                        <div class="d-flex gap-2">
                            <button type="button" class="btn btn-outline-primary" id="export-btn">
                                <i class="ri-file-download-line align-bottom"></i> Export
                            </button>
                            <button type="button" class="btn btn-outline-success" id="import-btn">
                                <i class="ri-upload-line align-bottom"></i> Import
                            </button>
                            <a href="<?php echo e(route('admin.students.create')); ?>" class="btn btn-primary">
                                <i class="ri-add-line align-bottom"></i> Tambah Baru
                            </a>
                        </div>
                    </div>
                </div>
                <!-- End Card Header -->

                <!-- Filter Section -->
                <div class="card-body border-bottom-dashed border-bottom">
                    <form id="filter-form" class="row g-3">
                        <!-- Status Dropdown -->
                        <div class="col-12 col-sm-6 col-md-3">
                            <div class="form-group">
                                <label for="filter-status" class="form-label">Status</label>
                                <select class="form-select" data-choices name="status" id="filter-status">
                                    <option value="">Semua Status</option>
                                    <?php $__currentLoopData = $statuses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $value => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($value); ?>"><?php echo e($label); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>
                        </div>

                        <!-- Gender Dropdown -->
                        <div class="col-12 col-sm-6 col-md-3">
                            <div class="form-group">
                                <label for="filter-gender" class="form-label">Jenis Kelamin</label>
                                <select class="form-select" data-choices name="gender" id="filter-gender">
                                    <option value="">Semua</option>
                                    <?php $__currentLoopData = $genders; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $value => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($value); ?>"><?php echo e($label); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>
                        </div>

                        <!-- Classroom Dropdown -->
                        <div class="col-12 col-sm-6 col-md-3">
                            <div class="form-group">
                                <label for="filter-classroom" class="form-label">Kelas</label>
                                <select class="form-select" data-choices id="filter-classroom" name="classroom">
                                    <option value="">Semua Kelas</option>
                                    <?php $__currentLoopData = $classrooms; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $classroom): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($classroom->id); ?>"><?php echo e($classroom->name); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>
                        </div>

                        <!-- Search Input -->
                        <div class="col-12 col-sm-6 col-md-3">
                            <div class="form-group">
                                <label for="search-input" class="form-label">Cari</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" placeholder="Cari nama, NIS, NISN..." id="search-input" name="search">
                                    <button class="btn btn-primary" type="button" id="search-button">
                                        <i class="ri-search-line"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <!-- End Filter Section -->

                <!-- Table Section -->
                <div class="card-body">
                    <div class="table-responsive">
                        <table id="students-table" class="table nowrap align-middle" style="width:100%">
                            <thead class="table-light text-muted">
                                <tr class="text-uppercase">
                                    <th>No</th>
                                    <th>NIS</th>
                                    <th>Nama Lengkap</th>
                                    <th>Email</th>
                                    <th>Jenis Kelamin</th>
                                    <th>Kelas</th>
                                    <th>Status</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody class="list">
                                <!-- Data will be loaded via AJAX -->
                            </tbody>
                        </table>
                    </div>
                </div>
                <!-- End Table Section -->
            </div>
        </div>
    </div>

    <!-- Modal untuk daftarkan siswa ke kelas -->
    <div class="modal fade" id="enrollModal" tabindex="-1" aria-labelledby="enrollModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="enrollModalLabel">Daftarkan Siswa ke Kelas</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form id="enrollForm" method="POST">
                    <?php echo csrf_field(); ?>
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="classroom_id" class="form-label">Pilih Kelas</label>
                            <select class="form-select" data-choices id="classroom_id" name="classroom_id" required>
                                <option value="">Pilih Kelas</option>
                                <!-- Kelas akan dimuat via AJAX -->
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="academic_year_id" class="form-label">Tahun Akademik</label>
                            <select class="form-select" data-choices id="academic_year_id" name="academic_year_id" required>
                                <option value="">Pilih Tahun Akademik</option>
                                <!-- Tahun akademik akan dimuat via AJAX -->
                            </select>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                        <button type="submit" class="btn btn-primary">Daftarkan</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Import Modal -->
    <div class="modal fade" id="importModal" tabindex="-1" aria-labelledby="importModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="importModalLabel">Import Data Siswa</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form id="importForm" method="POST" action="<?php echo e(route('admin.students.import')); ?>" enctype="multipart/form-data">
                    <?php echo csrf_field(); ?>
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="import_file" class="form-label">File Excel</label>
                            <input type="file" class="form-control" id="import_file" name="import_file" accept=".xlsx, .xls, .csv" required>
                            <small class="text-muted">Format: .xlsx, .xls, .csv</small>
                        </div>
                        <div class="mb-3">
                            <a href="<?php echo e(route('admin.students.template')); ?>" class="text-primary">
                                <i class="ri-download-line me-1"></i> Download Template
                            </a>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                        <button type="submit" class="btn btn-success">Import</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteModalLabel">Konfirmasi Hapus</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>Apakah Anda yakin ingin menghapus data siswa ini? Data yang dihapus tidak dapat dikembalikan.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="button" class="btn btn-danger" id="confirm-delete-btn">Hapus</button>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
    <style type="text/css">
        .dataTables_length,
        .dataTables_filter {
            display: none !important;
        }
    </style>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.partials.plugins.jquery', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
<?php echo $__env->make('admin.partials.plugins.datatables', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
<?php echo $__env->make('admin.partials.plugins.sweetalert', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

<?php $__env->startPush('scripts'); ?>
    <script>
        $(document).ready(function() {
            // Load classrooms for filter
            $.ajax({
                url: "<?php echo e(route('admin.ajax.classrooms.by.academic.year')); ?>",
                type: 'GET',
                data: {
                    academic_year_id: ''
                }, // Pass empty to get all classrooms
                success: function(response) {
                    if (response.success) {
                        var classroomSelect = $('#filter-classroom');
                        $.each(response.data, function(index, classroom) {
                            classroomSelect.append('<option value="' + classroom.id + '">' + classroom.name + '</option>');
                        });
                    } else {
                        console.error('Failed to load classrooms:', response.message);
                    }
                },
                error: function(xhr) {
                    console.error('Failed to load classrooms:', xhr.responseJSON?.message || 'Unknown error');
                }
            });

            // Initialize DataTable
            var dataTable = $('#students-table').DataTable({
                processing: true,
                serverSide: true,
                ajax: {
                    url: "<?php echo e(route('admin.students.index')); ?>",
                    data: function(d) {
                        d.status = $('#filter-status').val();
                        d.gender = $('#filter-gender').val();
                        d.classroom_id = $('#filter-classroom').val();
                        d.search = $('#search-input').val();
                    }
                },
                columns: [{
                        data: 'DT_RowIndex',
                        name: 'DT_RowIndex',
                        orderable: false,
                        searchable: false
                    },
                    {
                        data: 'nis',
                        name: 'nis'
                    },
                    {
                        data: 'user.name',
                        name: 'user.name'
                    },
                    {
                        data: 'user.email',
                        name: 'user.email'
                    },
                    {
                        data: 'gender',
                        name: 'gender'
                    },
                    {
                        data: 'classrooms',
                        name: 'classrooms',
                        orderable: false,
                        searchable: false
                    },
                    {
                        data: 'status',
                        name: 'status'
                    },
                    {
                        data: 'action',
                        name: 'action',
                        orderable: false,
                        searchable: false
                    }
                ],
                order: [
                    [2, 'asc']
                ], // Order by name
                language: {
                    processing: '<div class="spinner-border text-primary m-1" role="status"></div>',
                    searchPlaceholder: "Cari...",
                    lengthMenu: "Tampilkan _MENU_ data",
                    zeroRecords: "Data tidak ditemukan",
                    info: "Menampilkan _START_ sampai _END_ dari _TOTAL_ data",
                    infoEmpty: "Menampilkan 0 sampai 0 dari 0 data",
                    infoFiltered: "(disaring dari _MAX_ total data)",
                    paginate: {
                        first: "Pertama",
                        last: "Terakhir",
                        next: "Selanjutnya",
                        previous: "Sebelumnya"
                    }
                },
                drawCallback: function() {
                    // Update total count
                    $('#total-students').text(this.api().page.info().recordsTotal);

                    // Initialize tooltips
                    $('[data-bs-toggle="tooltip"]').tooltip();
                }
            });

            // Apply filters when changed
            $('#filter-status, #filter-gender, #filter-classroom').on('change', function() {
                dataTable.draw();
            });

            // Apply search filter
            $('#search-button').on('click', function() {
                dataTable.draw();
            });

            // Show import modal
            $('#import-btn').on('click', function() {
                $('#importModal').modal('show');
            });

            // Handle import form submission
            $('#importForm').on('submit', function(e) {
                e.preventDefault();
                var formData = new FormData(this);

                $.ajax({
                    url: $(this).attr('action'),
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    beforeSend: function() {
                        // Show loading state
                        $('#importForm button[type="submit"]').prop('disabled', true).html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Mengimpor...');
                    },
                    success: function(response) {
                        if (response.success) {
                            // Show success notification
                            Swal.fire({
                                title: 'Berhasil!',
                                text: response.message,
                                icon: 'success',
                                showConfirmButton: false,
                                timer: 1500
                            });

                            // Refresh datatable
                            dataTable.ajax.reload();

                            // Close modal
                            $('#importModal').modal('hide');
                        }
                    },
                    error: function(xhr) {
                        // Show error notification
                        Swal.fire({
                            title: 'Gagal!',
                            text: xhr.responseJSON?.message || 'Terjadi kesalahan saat mengimpor data',
                            icon: 'error'
                        });
                    },
                    complete: function() {
                        // Reset loading state
                        $('#importForm button[type="submit"]').prop('disabled', false).text('Import');
                    }
                });
            });

            // Handle export button click
            $('#export-btn').on('click', function() {
                var params = {
                    status: $('#filter-status').val(),
                    gender: $('#filter-gender').val(),
                    classroom_id: $('#filter-classroom').val(),
                    search: $('#search-input').val()
                };

                var url = "<?php echo e(route('admin.students.export')); ?>?" + $.param(params);
                window.location.href = url;
            });

            // Open enrollment modal
            $(document).on('click', '.enroll-btn', function() {
                openEnrollModal($(this).data('id'));
            });

            // Handle enrollment form submission
            $('#enrollForm').on('submit', function(e) {
                e.preventDefault();

                var formData = $(this).serialize();
                var studentId = $('#enrollForm').data('student-id');

                $.ajax({
                    url: "<?php echo e(url('admin/students')); ?>/" + studentId + "/enroll",
                    type: 'POST',
                    data: formData,
                    beforeSend: function() {
                        $('button[type="submit"]').prop('disabled', true).html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Menyimpan...');
                    },
                    success: function(response) {
                        if (response.success) {
                            $('#enrollModal').modal('hide');

                            Swal.fire({
                                icon: 'success',
                                title: 'Berhasil',
                                text: response.message,
                                showConfirmButton: false,
                                timer: 1500
                            });

                            // Refresh table
                            dataTable.ajax.reload();
                        }
                    },
                    error: function(xhr) {
                        if (xhr.status === 422) {
                            var errors = xhr.responseJSON.errors;
                            $.each(errors, function(key, value) {
                                $('#' + key).addClass('is-invalid');
                                $('#' + key).after('<div class="invalid-feedback">' + value[0] + '</div>');
                            });
                        } else {
                            Swal.fire({
                                icon: 'error',
                                title: 'Error',
                                text: xhr.responseJSON.message || 'Terjadi kesalahan pada server'
                            });
                        }
                    },
                    complete: function() {
                        $('button[type="submit"]').prop('disabled', false).text('Daftarkan');
                    }
                });
            });

            // Open delete confirmation modal
            $(document).on('click', '.delete-btn', function() {
                var id = $(this).data('id');
                $('#confirm-delete-btn').data('id', id);
                $('#deleteModal').modal('show');
            });

            // Handle delete confirmation
            $('#confirm-delete-btn').on('click', function() {
                var id = $(this).data('id');

                $.ajax({
                    url: "<?php echo e(url('admin/students')); ?>/" + id,
                    type: 'DELETE',
                    data: {
                        "_token": "<?php echo e(csrf_token()); ?>"
                    },
                    success: function(response) {
                        if (response.success) {
                            $('#deleteModal').modal('hide');

                            Swal.fire({
                                icon: 'success',
                                title: 'Berhasil',
                                text: response.message,
                                showConfirmButton: false,
                                timer: 1500
                            });

                            // Refresh table
                            dataTable.ajax.reload();
                        }
                    },
                    error: function(xhr) {
                        Swal.fire({
                            icon: 'error',
                            title: 'Error',
                            text: xhr.responseJSON?.message || 'Terjadi kesalahan pada server'
                        });
                    }
                });
            });

            // Clear validation errors when modal is closed
            $('.modal').on('hidden.bs.modal', function() {
                $('.is-invalid').removeClass('is-invalid');
                $('.invalid-feedback').remove();
                $(this).find('form')[0].reset();
            });
        });

        // Function to open enrollment modal and load data
        function openEnrollModal(studentId) {
            // Reset form
            $('#enrollForm')[0].reset();
            $('.is-invalid').removeClass('is-invalid');
            $('.invalid-feedback').remove();

            // Set student ID
            $('#enrollForm').data('student-id', studentId);

            // Load classrooms
            $.ajax({
                url: "<?php echo e(route('admin.ajax.classrooms.by.academic.year')); ?>",
                type: 'GET',
                data: {
                    academic_year_id: ''
                }, // Pass empty to get all classrooms
                success: function(response) {
                    var classroomSelect = $('#classroom_id');
                    classroomSelect.empty();
                    classroomSelect.append('<option value="">Pilih Kelas</option>');

                    if (response.success) {
                        $.each(response.data, function(index, classroom) {
                            classroomSelect.append('<option value="' + classroom.id + '">' + classroom.name + '</option>');
                        });
                    } else {
                        console.error('Failed to load classrooms:', response.message);
                    }
                },
                error: function(xhr) {
                    console.error('Failed to load classrooms:', xhr.responseJSON?.message || 'Unknown error');
                }
            });

            // Load academic years
            $.ajax({
                url: "<?php echo e(route('admin.ajax.academic-years.list')); ?>",
                type: 'GET',
                success: function(response) {
                    var academicYearSelect = $('#academic_year_id');
                    academicYearSelect.empty();
                    academicYearSelect.append('<option value="">Pilih Tahun Akademik</option>');

                    if (response.success) {
                        $.each(response.data, function(index, year) {
                            academicYearSelect.append('<option value="' + year.id + '">' + year.name + '</option>');
                        });
                    } else {
                        console.error('Failed to load academic years:', response.message);
                    }
                },
                error: function(xhr) {
                    console.error('Failed to load academic years:', xhr.responseJSON?.message || 'Unknown error');
                }
            });

            // Open modal
            $('#enrollModal').modal('show');
        }
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\rawooh-v2\resources\views/admin/pages/student/index.blade.php ENDPATH**/ ?>