<?php

namespace App\Repositories\Eloquent;

use App\Models\Teacher;
use App\Enums\UserStatus;
use App\Repositories\Contracts\TeacherRepositoryInterface;
use Illuminate\Database\Eloquent\Collection;

class TeacherRepository implements TeacherRepositoryInterface
{
    public function __construct(
        private Teacher $teacherModel,
    ) {
    }

    /**
     * Get all teachers with optional filtering.
     */
    public function getAll(array $filters = []): Collection
    {
        return $this->teacherModel->with('user')
            ->when(isset($filters['status']), fn($q) => $q->whereHas('user', fn($q) => $q->where('status', $filters['status'])))
            ->when(isset($filters['role']), fn($q) => $q->whereHas('user', fn($q) => $q->whereHas('roles', fn($q) => $q->where('name', $filters['role']))))
            ->when(isset($filters['search']), fn($q) => $q->whereHas('user', $this->getSearchFilter($filters['search'])))
            ->when(isset($filters['gender']), fn($q) => $q->where('gender', $filters['gender']))
            ->orderByDesc('created_at')
            ->get();
    }

    /**
     * Get all active teachers.
     */
    public function getAllActive(): Collection
    {
        return $this->teacherModel->with('user')
            ->whereHas('user', fn($q) => $q->where('status', UserStatus::Active->value))
            ->orderByDesc('created_at')
            ->get();
    }

    /**
     * Find a teacher by ID.
     */
    public function findById(int $id): Teacher
    {
        return $this->teacherModel->with('user')->findOrFail($id);
    }

    /**
     * Create a new teacher record.
     */
    public function create(array $data): Teacher
    {
        return $this->teacherModel->create($data);
    }

    /**
     * Update an existing teacher record.
     */
    public function update(int $id, array $data)
    {
        $teacher = $this->findById($id);
        $teacher->update($data);
    }

    /**
     * Delete a teacher record.
     */
    public function delete(int $id)
    {
        $teacher = $this->findById($id);
        $teacher->delete();
    }

    /**
     * Check if teacher has active assignments.
     */
    public function hasActiveAssignments(int $teacherId): bool
    {
        $teacher = $this->findById($teacherId);
        return $teacher->teacherAssignments()
            ->whereHas('academicYear', fn($q) => $q->where('status', 'active'))
            ->exists();
    }

    /**
     * Get homeroom teachers.
     */
    public function getHomeroomTeachers(): Collection
    {
        return $this->teacherModel->with(['user', 'teacherAssignments.classroom'])
            ->whereHas('teacherAssignments', fn($q) => $q->where('is_homeroom_teacher', true))
            ->whereHas('user', fn($q) => $q->where('status', UserStatus::Active->value))
            ->orderByDesc('created_at')
            ->get();
    }

    /**
     * Helper for search filtering on user attributes.
     */
    private function getSearchFilter(string $searchTerm): callable
    {
        $term = '%' . $searchTerm . '%';

        return fn($q) => $q->where('name', 'like', $term)
            ->orWhere('email', 'like', $term)
            ->orWhere('username', 'like', $term)
            ->orWhere('phone_number', 'like', $term);
    }
}
